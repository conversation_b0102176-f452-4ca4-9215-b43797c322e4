//function tools definitions for LLM integration based on sheetHandler.js

//--------- ANALYSE DATA AGENT --------------//
//agent phân tích dữ liệu với khả năng gọi nhiều function liên tiếp
function analyseDataAgent(userQuestion) {
  // Use the actual user question passed as parameter
  userQuestion = "Phân tích tình hình ăn ngoài của gia đình tôi 3 tháng gần nhất và đưa ra lời khuyên cải thiện.";

  const apiKey = OPENAI_TOKEN;
  const currentDate = Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "dd/MM/yyyy");

  // Step 1: Create system prompt
  const systemPrompt = `
    The current date is ${currentDate}. The date format is dd/MM/yyyy.

    # Identity
    You are a personal financial coach talking to your customer via Telegram.
    Your name is <PERSON>, communicating with users via Telegram.
    Be frank and firm.

    # Instructions
    Based on the customer goal, close with any final advice or truths the customer may need to hear - especially things they might resist but need to confront to achieve their goal.
    Use the following language: Vietnamese
    Don't just rely on the tools, plan and think of all the steps to solve the customer question.

    Use the available tools to gather the necessary financial data, then provide your comprehensive analysis in Vietnamese.
    `;

  // Step 2: Initialize conversation for /responses endpoint

  let stepCount = 0;
  const maxSteps = 7;
  let conversationHistory = [
    { role: "system", content: systemPrompt },
    { role: "user", content: userQuestion }
  ];

  try {
    // Step 3: Execute iterative function calling
    while (stepCount < maxSteps) {
      stepCount++;

      Logger.log(`Step ${stepCount}: Making OpenAI API call`);

      // Create payload for /responses endpoint
      const payload = {
        model: "gpt-4.1",
        input: conversationHistory,
        temperature: 0.5,
        tools: tools
      };

      const response = UrlFetchApp.fetch("https://api.openai.com/v1/responses", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json"
        },
        payload: JSON.stringify(payload),
        muteHttpExceptions: true
      });

      const json = JSON.parse(response.getContentText());

      if (json.error) {
        Logger.log (json.error.message);
      }

      if (!json.output || !Array.isArray(json.output)) {
        throw new Error("Invalid OpenAI response");
      }

      // Handle different types of outputs
      let assistantMessage = { role: "assistant", content: "" };
      let functionCalls = [];

      for (const output of json.output) {
        if (output.type === "function_call") {
          functionCalls.push(output);
        } else if (output.content && output.content[0] && output.content[0].text) {
          assistantMessage.content = output.content[0].text;
        }
      }

      conversationHistory.push(assistantMessage);

      // Process function calls if any
      if (functionCalls.length > 0) {
        Logger.log(`Step ${stepCount}: Processing ${functionCalls.length} function calls`);

        // Execute each function call
        for (const functionCall of functionCalls) {
          const functionName = functionCall.name;
          const functionArgs = JSON.parse(functionCall.arguments || "{}");

          Logger.log(`Executing function: ${functionName} with args:`, functionArgs);

          let functionResult;
          try {
            // Execute the function based on its name
            switch (functionName) {
              case "getBudgetData":
                functionResult = getBudgetData(functionArgs.monthText);
                break;
              case "getDashboardData":
                functionResult = getDashboardData(functionArgs.monthText);
                break;
              case "findTransactionRowById":
                functionResult = findTransactionRowById(functionArgs.sheetName, functionArgs.transactionId);
                break;
              case "getFundBalances":
                functionResult = getFundBalances(functionArgs.type);
                break;
              case "getTxCat":
                functionResult = getTxCat();
                break;
              case "getFamilyContext":
                functionResult = getFamilyContext();
                break;
              case "getCategoriseInstructions":
                functionResult = getCategoriseInstructions();
                break;
              case "getBudgetInstructions":
                functionResult = getBudgetInstructions();
                break;
              case "searchTransactions":
                functionResult = searchTx({
                  startDate: functionArgs.startDate,
                  endDate: functionArgs.endDate,
                  groups: functionArgs.groups || [],
                  categories: functionArgs.categories || [],
                  keywords: functionArgs.keywords || []
                });
                break;
              default:
                functionResult = { error: `Unknown function: ${functionName}` };
            }
          } catch (error) {
            functionResult = { error: `Error executing ${functionName}: ${error.toString()}` };
          }

          // Add function result to conversation
          conversationHistory.push({
            type: "function_call_output",
            call_id: functionCall.call_id,
            output: JSON.stringify(functionResult)
          });
        }

        // Continue to next iteration to get AI's response to function results
        continue;
      }

      // If no function calls and we have content, this is the final response
      if (assistantMessage.content && assistantMessage.content.trim()) {
        Logger.log(`Step ${stepCount}: Got final response from AI`);

        // Step 4: Send response via Telegram
        sendTelegramMessage(assistantMessage.content);

        return {
          success: true,
          response: assistantMessage.content,
          steps: stepCount
        };
      }
    }

    // If we reach max steps without completion
    const fallbackMessage = "😅 Xin lỗi, tôi cần nhiều thời gian hơn để phân tích yêu cầu của bạn. Bạn có thể thử lại với câu hỏi cụ thể hơn không?";
    sendTelegramMessage(fallbackMessage);

    return {
      success: false,
      error: "Reached maximum steps without completion",
      steps: stepCount
    };

  } catch (error) {
    Logger.log("Error in analyseDataAgent:", error);
    const errorMessage = "😱 Đã xảy ra lỗi khi phân tích dữ liệu. Vui lòng thử lại sau.";
    sendTelegramMessage(errorMessage);

    return {
      success: false,
      error: error.toString(),
      steps: stepCount
    };
  }
}