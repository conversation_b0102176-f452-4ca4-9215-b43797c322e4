//function tools definitions for LLM integration based on sheetHandler.js

//--------- ANALYSE DATA AGENT --------------//
//agent phân tích dữ liệu với khả năng gọi nhiều function liên tiếp
function analyseDataAgent(userQuestion) {
  // Use the actual user question passed as parameter

  const apiKey = OPENAI_TOKEN;
  const currentDate = Utilities.formatDate(new Date(), Session.getScriptTimeZone(), "dd/MM/yyyy");

  // Step 1: Create system prompt
  const systemPrompt = `
    The current date is ${currentDate}. The date format is dd/MM/yyyy.

    # Identity
    You are a personal financial coach talking to your customer via Telegram.
    Your name is <PERSON>, communicating with users via Telegram.
    Be frank and firm.

    # Instructions
    Based on the customer goal, close with any final advice or truths the customer may need to hear - especially things they might resist but need to confront to achieve their goal.
    Use the following language: Vietnamese
    Don't just rely on the tools, plan and think of all the steps to solve the customer question.

    When you need to call functions, respond with JSON in this format:
    {
        "functions": [
            {"function_name": "getBudgetData", "params": ["12/2024"]},
            {"function_name": "getFundBalances", "params": ["all"]}
        ],
        "last_step": "no" or "yes" if this is the last step to answer the customer question
    }

    When you have all the data needed, provide your final analysis in Vietnamese without JSON format.
    `;

  // Step 2: Initialize conversation for /responses endpoint

  let stepCount = 0;
  const maxSteps = 7;
  let conversationHistory = [
    { role: "system", content: systemPrompt },
    { role: "user", content: userQuestion }
  ];

  try {
    // Step 3: Execute iterative function calling
    while (stepCount < maxSteps) {
      stepCount++;

      Logger.log(`Step ${stepCount}: Making OpenAI API call`);

      // Create payload for /responses endpoint
      const payload = {
        model: "gpt-4o",
        input: conversationHistory,
        temperature: 0.5
        too
      };

      const response = UrlFetchApp.fetch("https://api.openai.com/v1/responses", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${apiKey}`,
          "Content-Type": "application/json"
        },
        payload: JSON.stringify(payload),
        muteHttpExceptions: true
      });

      const json = JSON.parse(response.getContentText());

      if (!json.output || !json.output[0] || !json.output[0].content || !json.output[0].content[0]) {
        throw new Error("Invalid OpenAI response");
      }

      const content = json.output[0].content[0].text;
      const assistantMessage = { role: "assistant", content: content };
      conversationHistory.push(assistantMessage);

      // Check if AI wants to call functions by parsing the content
      let functionCalls = [];
      try {
        // Try to parse the response as JSON to see if it contains function calls
        const parsedContent = JSON.parse(content);
        if (parsedContent.functions && Array.isArray(parsedContent.functions)) {
          functionCalls = parsedContent.functions;
        }
      } catch (e) {
        // If not JSON, check if this is the final response
        Logger.log(`Step ${stepCount}: Got final response from AI`);
        sendTelegramMessage(content);
        return {
          success: true,
          response: content,
          steps: stepCount
        };
      }

      if (functionCalls.length > 0) {
        Logger.log(`Step ${stepCount}: Processing ${functionCalls.length} function calls`);

        // Execute each function call
        for (const functionCall of functionCalls) {
          const functionName = functionCall.function_name;
          const functionArgs = functionCall.params || {};

          Logger.log(`Executing function: ${functionName} with args:`, functionArgs);

          let functionResult;
          try {
            // Execute the function based on its name
            switch (functionName) {
              case "getBudgetData":
                functionResult = getBudgetData(functionArgs[0] || functionArgs.monthText);
                break;
              case "getDashboardData":
                functionResult = getDashboardData(functionArgs[0] || functionArgs.monthText);
                break;
              case "findTransactionRowById":
                functionResult = findTransactionRowById(
                  functionArgs[0] || functionArgs.sheetName,
                  functionArgs[1] || functionArgs.transactionId
                );
                break;
              case "getFundBalances":
                functionResult = getFundBalances(functionArgs[0] || functionArgs.type || "all");
                break;
              case "getTxCat":
                functionResult = getTxCat();
                break;
              case "getFamilyContext":
                functionResult = getFamilyContext();
                break;
              case "getCategoriseInstructions":
                functionResult = getCategoriseInstructions();
                break;
              case "getBudgetInstructions":
                functionResult = getBudgetInstructions();
                break;
              case "searchTransactions":
                functionResult = searchTx({
                  startDate: functionArgs[0] || functionArgs.startDate,
                  endDate: functionArgs[1] || functionArgs.endDate,
                  groups: functionArgs[2] || functionArgs.groups || [],
                  categories: functionArgs[3] || functionArgs.categories || [],
                  keywords: functionArgs[4] || functionArgs.keywords || []
                });
                break;
              default:
                functionResult = { error: `Unknown function: ${functionName}` };
            }
          } catch (error) {
            functionResult = { error: `Error executing ${functionName}: ${error.toString()}` };
          }

          // Add function result to conversation
          conversationHistory.push({
            role: "user",
            content: `Function ${functionName} result: ${JSON.stringify(functionResult)}`
          });
        }

        // Continue to next iteration to get AI's response to function results
        continue;
      }
    }

    // If we reach max steps without completion
    const fallbackMessage = "😅 Xin lỗi, tôi cần nhiều thời gian hơn để phân tích yêu cầu của bạn. Bạn có thể thử lại với câu hỏi cụ thể hơn không?";
    sendTelegramMessage(fallbackMessage);

    return {
      success: false,
      error: "Reached maximum steps without completion",
      steps: stepCount
    };

  } catch (error) {
    Logger.log("Error in analyseDataAgent:", error);
    const errorMessage = "😱 Đã xảy ra lỗi khi phân tích dữ liệu. Vui lòng thử lại sau.";
    sendTelegramMessage(errorMessage);

    return {
      success: false,
      error: error.toString(),
      steps: stepCount
    };
  }
}