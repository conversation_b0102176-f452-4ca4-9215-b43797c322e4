# 🔒 Ignore config files with secrets or credentials
configurations.js
*.env
.env.*

# 🗃️ Ignore dependency directories
node_modules/
vendor/

# 💻 Ignore system files
.DS_Store
Thumbs.db

# 🧪 Ignore log files and test outputs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# 🧪 Ignore build artifacts
dist/
build/
out/

# 🔁 Ignore local git state
*.swp
*.swo
*.tmp

# 📦 Ignore lockfiles if you use Yarn or NPM
package-lock.json
yarn.lock
